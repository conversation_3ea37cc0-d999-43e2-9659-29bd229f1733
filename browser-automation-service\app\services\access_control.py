"""
Tier-Based Access Control Service
Implements subscription-based feature access and quota management for browser automation
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from enum import Enum
from dataclasses import dataclass, field
import json

from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserAutomationException


class SubscriptionTier(Enum):
    """RouKey subscription tiers"""
    FREE = "free"
    STARTER = "starter"
    PRO = "pro"
    ENTERPRISE = "enterprise"


class FeatureType(Enum):
    """Browser automation features"""
    BASIC_BROWSING = "basic_browsing"
    ADVANCED_EXTRACTION = "advanced_extraction"
    VERIFICATION = "verification"
    PARALLEL_PROCESSING = "parallel_processing"
    CUSTOM_WORKFLOWS = "custom_workflows"
    PRIORITY_SUPPORT = "priority_support"
    ADVANCED_MEMORY = "advanced_memory"
    BULK_OPERATIONS = "bulk_operations"
    API_ACCESS = "api_access"
    CUSTOM_INTEGRATIONS = "custom_integrations"


class QuotaType(Enum):
    """Types of usage quotas"""
    BROWSING_TASKS = "browsing_tasks"
    API_CALLS = "api_calls"
    CONCURRENT_SESSIONS = "concurrent_sessions"
    STORAGE_MB = "storage_mb"
    VERIFICATION_REQUESTS = "verification_requests"
    CUSTOM_WORKFLOWS = "custom_workflows"


@dataclass
class TierLimits:
    """Limits and features for a subscription tier"""
    tier: SubscriptionTier
    monthly_browsing_tasks: int
    concurrent_sessions: int
    max_workflow_complexity: int
    storage_mb: int
    verification_requests: int
    custom_workflows: int
    api_calls_per_hour: int
    features: Set[FeatureType] = field(default_factory=set)
    priority_level: int = 1
    support_level: str = "community"


@dataclass
class UserQuota:
    """Current usage and limits for a user"""
    user_id: str
    tier: SubscriptionTier
    current_period_start: datetime
    current_period_end: datetime
    usage: Dict[QuotaType, int] = field(default_factory=dict)
    limits: Dict[QuotaType, int] = field(default_factory=dict)
    overages: Dict[QuotaType, int] = field(default_factory=dict)


class AccessControlManager(LoggerMixin):
    """
    Comprehensive tier-based access control system
    
    Features:
    - Subscription tier management and validation
    - Feature access control based on subscription
    - Usage quota tracking and enforcement
    - Overage detection and handling
    - Real-time usage monitoring
    - Automatic quota resets
    - Usage analytics and reporting
    """
    
    def __init__(self):
        # Tier configurations
        self.tier_limits = self._initialize_tier_limits()
        
        # User quota tracking
        self.user_quotas: Dict[str, UserQuota] = {}
        self.usage_history: List[Dict[str, Any]] = []
        
        # Access control cache
        self.access_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 300  # 5 minutes
        
        # Usage tracking
        self.usage_stats = {
            "total_requests": 0,
            "blocked_requests": 0,
            "quota_exceeded": 0,
            "feature_restricted": 0,
            "tier_distribution": {}
        }
        
        self.log_info("Access control manager initialized")
    
    def _initialize_tier_limits(self) -> Dict[SubscriptionTier, TierLimits]:
        """Initialize tier limits and features"""
        try:
            tier_limits = {
                SubscriptionTier.FREE: TierLimits(
                    tier=SubscriptionTier.FREE,
                    monthly_browsing_tasks=0,  # No browsing for free tier
                    concurrent_sessions=0,
                    max_workflow_complexity=0,
                    storage_mb=0,
                    verification_requests=0,
                    custom_workflows=0,
                    api_calls_per_hour=0,
                    features=set(),  # No browser automation features
                    priority_level=1,
                    support_level="community"
                ),
                
                SubscriptionTier.STARTER: TierLimits(
                    tier=SubscriptionTier.STARTER,
                    monthly_browsing_tasks=10,  # Fixed to match milestone document
                    concurrent_sessions=1,
                    max_workflow_complexity=3,  # Max 3 roles
                    storage_mb=100,
                    verification_requests=50,
                    custom_workflows=2,
                    api_calls_per_hour=100,
                    features={
                        FeatureType.BASIC_BROWSING,
                        FeatureType.ADVANCED_EXTRACTION,
                        FeatureType.VERIFICATION
                    },
                    priority_level=2,
                    support_level="email"
                ),
                
                SubscriptionTier.PRO: TierLimits(
                    tier=SubscriptionTier.PRO,
                    monthly_browsing_tasks=-1,  # Unlimited as per milestone document
                    concurrent_sessions=3,
                    max_workflow_complexity=10,
                    storage_mb=500,
                    verification_requests=500,
                    custom_workflows=10,
                    api_calls_per_hour=500,
                    features={
                        FeatureType.BASIC_BROWSING,
                        FeatureType.ADVANCED_EXTRACTION,
                        FeatureType.VERIFICATION,
                        FeatureType.PARALLEL_PROCESSING,
                        FeatureType.CUSTOM_WORKFLOWS,
                        FeatureType.ADVANCED_MEMORY,
                        FeatureType.API_ACCESS
                    },
                    priority_level=3,
                    support_level="priority"
                ),
                
                SubscriptionTier.ENTERPRISE: TierLimits(
                    tier=SubscriptionTier.ENTERPRISE,
                    monthly_browsing_tasks=1000,
                    concurrent_sessions=10,
                    max_workflow_complexity=-1,  # Unlimited
                    storage_mb=5000,
                    verification_requests=5000,
                    custom_workflows=50,
                    api_calls_per_hour=2000,
                    features={
                        FeatureType.BASIC_BROWSING,
                        FeatureType.ADVANCED_EXTRACTION,
                        FeatureType.VERIFICATION,
                        FeatureType.PARALLEL_PROCESSING,
                        FeatureType.CUSTOM_WORKFLOWS,
                        FeatureType.PRIORITY_SUPPORT,
                        FeatureType.ADVANCED_MEMORY,
                        FeatureType.BULK_OPERATIONS,
                        FeatureType.API_ACCESS,
                        FeatureType.CUSTOM_INTEGRATIONS
                    },
                    priority_level=4,
                    support_level="dedicated"
                )
            }
            
            self.log_info(f"Initialized {len(tier_limits)} subscription tiers")
            return tier_limits
            
        except Exception as e:
            self.log_error(f"Failed to initialize tier limits: {e}")
            return {}
    
    async def check_access(
        self,
        user_id: str,
        feature: FeatureType,
        resource_requirements: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Check if user has access to a specific feature
        
        Args:
            user_id: User identifier
            feature: Feature to check access for
            resource_requirements: Additional resource requirements
            
        Returns:
            Dictionary containing access decision and details
        """
        try:
            self.usage_stats["total_requests"] += 1
            
            # Get user's subscription tier and quota
            user_quota = await self._get_user_quota(user_id)
            
            if not user_quota:
                self.usage_stats["blocked_requests"] += 1
                return {
                    "access_granted": False,
                    "reason": "User quota not found",
                    "tier": "unknown",
                    "feature": feature.value
                }
            
            tier_limits = self.tier_limits[user_quota.tier]
            
            # Check feature access
            if feature not in tier_limits.features:
                self.usage_stats["feature_restricted"] += 1
                return {
                    "access_granted": False,
                    "reason": f"Feature {feature.value} not available in {user_quota.tier.value} tier",
                    "tier": user_quota.tier.value,
                    "feature": feature.value,
                    "required_tier": await self._get_minimum_tier_for_feature(feature)
                }
            
            # Check quota limits
            quota_check = await self._check_quota_limits(user_quota, resource_requirements)
            
            if not quota_check["within_limits"]:
                self.usage_stats["quota_exceeded"] += 1
                return {
                    "access_granted": False,
                    "reason": f"Quota exceeded: {quota_check['exceeded_quotas']}",
                    "tier": user_quota.tier.value,
                    "feature": feature.value,
                    "quota_status": quota_check
                }
            
            # Access granted
            access_result = {
                "access_granted": True,
                "tier": user_quota.tier.value,
                "feature": feature.value,
                "quota_status": quota_check,
                "tier_limits": {
                    "monthly_browsing_tasks": tier_limits.monthly_browsing_tasks,
                    "concurrent_sessions": tier_limits.concurrent_sessions,
                    "max_workflow_complexity": tier_limits.max_workflow_complexity
                }
            }
            
            # Cache the result
            await self._cache_access_result(user_id, feature, access_result)
            
            self.log_info(f"Access granted for user {user_id}: {feature.value}")
            
            return access_result
            
        except Exception as e:
            self.log_error(f"Access check failed: {e}")
            self.usage_stats["blocked_requests"] += 1
            return {
                "access_granted": False,
                "reason": f"Access check error: {e}",
                "tier": "unknown",
                "feature": feature.value
            }
    
    async def consume_quota(
        self,
        user_id: str,
        quota_type: QuotaType,
        amount: int = 1,
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Consume user quota for a specific resource
        
        Args:
            user_id: User identifier
            quota_type: Type of quota to consume
            amount: Amount to consume
            metadata: Additional metadata about the usage
            
        Returns:
            Dictionary containing consumption result
        """
        try:
            user_quota = await self._get_user_quota(user_id)
            
            if not user_quota:
                return {
                    "success": False,
                    "reason": "User quota not found",
                    "remaining": 0
                }
            
            # Check if quota type exists for user's tier
            if quota_type not in user_quota.limits:
                return {
                    "success": False,
                    "reason": f"Quota type {quota_type.value} not available for tier {user_quota.tier.value}",
                    "remaining": 0
                }
            
            current_usage = user_quota.usage.get(quota_type, 0)
            quota_limit = user_quota.limits[quota_type]
            
            # Check if consumption would exceed limit
            if current_usage + amount > quota_limit:
                # Track overage
                overage = (current_usage + amount) - quota_limit
                user_quota.overages[quota_type] = user_quota.overages.get(quota_type, 0) + overage
                
                return {
                    "success": False,
                    "reason": f"Quota limit exceeded for {quota_type.value}",
                    "current_usage": current_usage,
                    "quota_limit": quota_limit,
                    "requested_amount": amount,
                    "overage": overage,
                    "remaining": max(0, quota_limit - current_usage)
                }
            
            # Consume quota
            user_quota.usage[quota_type] = current_usage + amount
            
            # Record usage
            await self._record_usage(user_id, quota_type, amount, metadata)
            
            remaining = quota_limit - user_quota.usage[quota_type]
            
            self.log_info(
                f"Quota consumed for user {user_id}",
                quota_type=quota_type.value,
                amount=amount,
                remaining=remaining
            )
            
            return {
                "success": True,
                "quota_type": quota_type.value,
                "amount_consumed": amount,
                "current_usage": user_quota.usage[quota_type],
                "quota_limit": quota_limit,
                "remaining": remaining,
                "percentage_used": (user_quota.usage[quota_type] / quota_limit) * 100
            }
            
        except Exception as e:
            self.log_error(f"Quota consumption failed: {e}")
            return {
                "success": False,
                "reason": f"Quota consumption error: {e}",
                "remaining": 0
            }
    
    async def get_user_limits(self, user_id: str) -> Dict[str, Any]:
        """
        Get comprehensive limits and usage for a user
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary containing user limits and current usage
        """
        try:
            user_quota = await self._get_user_quota(user_id)
            
            if not user_quota:
                return {
                    "error": "User quota not found",
                    "user_id": user_id
                }
            
            tier_limits = self.tier_limits[user_quota.tier]
            
            # Calculate usage percentages
            usage_percentages = {}
            for quota_type, limit in user_quota.limits.items():
                current_usage = user_quota.usage.get(quota_type, 0)
                if limit > 0:
                    usage_percentages[quota_type.value] = (current_usage / limit) * 100
                else:
                    usage_percentages[quota_type.value] = 0
            
            # Get time until quota reset
            time_until_reset = user_quota.current_period_end - datetime.now()
            
            return {
                "user_id": user_id,
                "subscription_tier": user_quota.tier.value,
                "current_period": {
                    "start": user_quota.current_period_start.isoformat(),
                    "end": user_quota.current_period_end.isoformat(),
                    "days_remaining": time_until_reset.days,
                    "hours_remaining": time_until_reset.seconds // 3600
                },
                "limits": {
                    "monthly_browsing_tasks": tier_limits.monthly_browsing_tasks,
                    "concurrent_sessions": tier_limits.concurrent_sessions,
                    "max_workflow_complexity": tier_limits.max_workflow_complexity,
                    "storage_mb": tier_limits.storage_mb,
                    "verification_requests": tier_limits.verification_requests,
                    "custom_workflows": tier_limits.custom_workflows,
                    "api_calls_per_hour": tier_limits.api_calls_per_hour
                },
                "current_usage": {
                    quota_type.value: user_quota.usage.get(quota_type, 0)
                    for quota_type in user_quota.limits.keys()
                },
                "usage_percentages": usage_percentages,
                "overages": {
                    quota_type.value: user_quota.overages.get(quota_type, 0)
                    for quota_type in user_quota.overages.keys()
                },
                "available_features": [feature.value for feature in tier_limits.features],
                "priority_level": tier_limits.priority_level,
                "support_level": tier_limits.support_level
            }
            
        except Exception as e:
            self.log_error(f"Failed to get user limits: {e}")
            return {
                "error": str(e),
                "user_id": user_id
            }
    
    async def check_browsing_quota(self, user_id: str) -> Dict[str, Any]:
        """
        Specific check for browsing task quota
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary containing browsing quota status
        """
        try:
            user_quota = await self._get_user_quota(user_id)
            
            if not user_quota:
                return {
                    "can_browse": False,
                    "reason": "User quota not found",
                    "remaining_tasks": 0
                }
            
            # Check if browsing is available for this tier
            tier_limits = self.tier_limits[user_quota.tier]
            
            if FeatureType.BASIC_BROWSING not in tier_limits.features:
                return {
                    "can_browse": False,
                    "reason": f"Browsing not available in {user_quota.tier.value} tier",
                    "remaining_tasks": 0,
                    "required_tier": "starter"
                }
            
            # Check browsing task quota
            current_usage = user_quota.usage.get(QuotaType.BROWSING_TASKS, 0)
            quota_limit = user_quota.limits.get(QuotaType.BROWSING_TASKS, 0)
            remaining_tasks = max(0, quota_limit - current_usage)
            
            if remaining_tasks <= 0:
                return {
                    "can_browse": False,
                    "reason": "Monthly browsing task quota exceeded",
                    "current_usage": current_usage,
                    "quota_limit": quota_limit,
                    "remaining_tasks": 0,
                    "reset_date": user_quota.current_period_end.isoformat()
                }
            
            return {
                "can_browse": True,
                "remaining_tasks": remaining_tasks,
                "current_usage": current_usage,
                "quota_limit": quota_limit,
                "percentage_used": (current_usage / quota_limit) * 100 if quota_limit > 0 else 0,
                "reset_date": user_quota.current_period_end.isoformat()
            }
            
        except Exception as e:
            self.log_error(f"Browsing quota check failed: {e}")
            return {
                "can_browse": False,
                "reason": f"Quota check error: {e}",
                "remaining_tasks": 0
            }
    
    async def upgrade_tier_simulation(
        self,
        user_id: str,
        target_tier: SubscriptionTier
    ) -> Dict[str, Any]:
        """
        Simulate what would be available with a tier upgrade
        
        Args:
            user_id: User identifier
            target_tier: Target subscription tier
            
        Returns:
            Dictionary containing upgrade benefits
        """
        try:
            current_quota = await self._get_user_quota(user_id)
            current_tier = current_quota.tier if current_quota else SubscriptionTier.FREE
            
            current_limits = self.tier_limits[current_tier]
            target_limits = self.tier_limits[target_tier]
            
            # Calculate improvements
            improvements = {
                "monthly_browsing_tasks": {
                    "current": current_limits.monthly_browsing_tasks,
                    "upgraded": target_limits.monthly_browsing_tasks,
                    "increase": target_limits.monthly_browsing_tasks - current_limits.monthly_browsing_tasks
                },
                "concurrent_sessions": {
                    "current": current_limits.concurrent_sessions,
                    "upgraded": target_limits.concurrent_sessions,
                    "increase": target_limits.concurrent_sessions - current_limits.concurrent_sessions
                },
                "max_workflow_complexity": {
                    "current": current_limits.max_workflow_complexity,
                    "upgraded": target_limits.max_workflow_complexity,
                    "increase": "unlimited" if target_limits.max_workflow_complexity == -1 else target_limits.max_workflow_complexity - current_limits.max_workflow_complexity
                }
            }
            
            # New features
            new_features = target_limits.features - current_limits.features
            
            return {
                "current_tier": current_tier.value,
                "target_tier": target_tier.value,
                "improvements": improvements,
                "new_features": [feature.value for feature in new_features],
                "support_upgrade": f"{current_limits.support_level} → {target_limits.support_level}",
                "priority_upgrade": f"Level {current_limits.priority_level} → Level {target_limits.priority_level}"
            }
            
        except Exception as e:
            self.log_error(f"Tier upgrade simulation failed: {e}")
            return {
                "error": str(e),
                "current_tier": "unknown",
                "target_tier": target_tier.value
            }
    
    async def get_access_analytics(
        self,
        time_period: str = "24h",
        user_id: str = None
    ) -> Dict[str, Any]:
        """
        Get access control analytics
        
        Args:
            time_period: Time period for analysis
            user_id: Specific user to analyze
            
        Returns:
            Dictionary containing access analytics
        """
        try:
            analytics = {
                "usage_statistics": self.usage_stats.copy(),
                "tier_distribution": await self._calculate_tier_distribution(),
                "quota_utilization": await self._calculate_quota_utilization(),
                "feature_usage": await self._calculate_feature_usage(),
                "upgrade_opportunities": await self._identify_upgrade_opportunities()
            }
            
            # Calculate derived metrics
            total_requests = self.usage_stats["total_requests"]
            if total_requests > 0:
                analytics["usage_statistics"]["block_rate"] = (self.usage_stats["blocked_requests"] / total_requests) * 100
                analytics["usage_statistics"]["quota_exceeded_rate"] = (self.usage_stats["quota_exceeded"] / total_requests) * 100
                analytics["usage_statistics"]["feature_restriction_rate"] = (self.usage_stats["feature_restricted"] / total_requests) * 100
            
            return analytics
            
        except Exception as e:
            self.log_error(f"Access analytics failed: {e}")
            return {"error": str(e)}

    # Helper methods for access control operations
    async def _get_user_quota(self, user_id: str) -> Optional[UserQuota]:
        """Get or create user quota"""
        try:
            if user_id not in self.user_quotas:
                # Create new user quota - would normally fetch from database
                user_quota = await self._create_user_quota(user_id)
                self.user_quotas[user_id] = user_quota

            user_quota = self.user_quotas[user_id]

            # Check if quota period needs reset
            if datetime.now() >= user_quota.current_period_end:
                await self._reset_user_quota(user_quota)

            return user_quota

        except Exception as e:
            self.log_error(f"Failed to get user quota: {e}")
            return None

    async def _create_user_quota(self, user_id: str) -> UserQuota:
        """Create new user quota with default tier"""
        try:
            # Default to STARTER tier for new users
            # In production, this would fetch from user's subscription
            tier = SubscriptionTier.STARTER
            tier_limits = self.tier_limits[tier]

            # Set current period (monthly)
            now = datetime.now()
            period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # Calculate next month
            if period_start.month == 12:
                period_end = period_start.replace(year=period_start.year + 1, month=1)
            else:
                period_end = period_start.replace(month=period_start.month + 1)

            # Initialize quota limits
            limits = {
                QuotaType.BROWSING_TASKS: tier_limits.monthly_browsing_tasks,
                QuotaType.CONCURRENT_SESSIONS: tier_limits.concurrent_sessions,
                QuotaType.STORAGE_MB: tier_limits.storage_mb,
                QuotaType.VERIFICATION_REQUESTS: tier_limits.verification_requests,
                QuotaType.CUSTOM_WORKFLOWS: tier_limits.custom_workflows,
                QuotaType.API_CALLS: tier_limits.api_calls_per_hour
            }

            # Initialize usage to zero
            usage = {quota_type: 0 for quota_type in limits.keys()}

            user_quota = UserQuota(
                user_id=user_id,
                tier=tier,
                current_period_start=period_start,
                current_period_end=period_end,
                usage=usage,
                limits=limits,
                overages={}
            )

            self.log_info(f"Created user quota for {user_id}", tier=tier.value)

            return user_quota

        except Exception as e:
            self.log_error(f"Failed to create user quota: {e}")
            raise BrowserAutomationException(f"User quota creation failed: {e}")

    async def _reset_user_quota(self, user_quota: UserQuota):
        """Reset user quota for new period"""
        try:
            # Reset usage
            for quota_type in user_quota.usage.keys():
                user_quota.usage[quota_type] = 0

            # Clear overages
            user_quota.overages.clear()

            # Update period
            old_end = user_quota.current_period_end
            user_quota.current_period_start = old_end

            # Calculate next period end
            if old_end.month == 12:
                user_quota.current_period_end = old_end.replace(year=old_end.year + 1, month=1)
            else:
                user_quota.current_period_end = old_end.replace(month=old_end.month + 1)

            self.log_info(f"Reset quota for user {user_quota.user_id}")

        except Exception as e:
            self.log_error(f"Failed to reset user quota: {e}")

    async def _check_quota_limits(
        self,
        user_quota: UserQuota,
        resource_requirements: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Check if user is within quota limits"""
        try:
            resource_requirements = resource_requirements or {}

            within_limits = True
            exceeded_quotas = []
            quota_status = {}

            for quota_type, limit in user_quota.limits.items():
                current_usage = user_quota.usage.get(quota_type, 0)
                required_amount = resource_requirements.get(quota_type.value, 0)

                # Check if adding required amount would exceed limit
                if current_usage + required_amount > limit:
                    within_limits = False
                    exceeded_quotas.append(quota_type.value)

                quota_status[quota_type.value] = {
                    "current_usage": current_usage,
                    "limit": limit,
                    "required": required_amount,
                    "remaining": max(0, limit - current_usage),
                    "would_exceed": current_usage + required_amount > limit
                }

            return {
                "within_limits": within_limits,
                "exceeded_quotas": exceeded_quotas,
                "quota_status": quota_status
            }

        except Exception as e:
            self.log_error(f"Quota limit check failed: {e}")
            return {
                "within_limits": False,
                "exceeded_quotas": ["check_error"],
                "quota_status": {}
            }

    async def _get_minimum_tier_for_feature(self, feature: FeatureType) -> str:
        """Get minimum tier required for a feature"""
        try:
            for tier, limits in self.tier_limits.items():
                if feature in limits.features:
                    return tier.value

            return "enterprise"  # Default to highest tier

        except Exception:
            return "unknown"

    async def _cache_access_result(
        self,
        user_id: str,
        feature: FeatureType,
        result: Dict[str, Any]
    ):
        """Cache access control result"""
        try:
            cache_key = f"{user_id}:{feature.value}"
            self.access_cache[cache_key] = {
                "result": result,
                "timestamp": datetime.now(),
                "ttl": self.cache_ttl
            }

            # Clean old cache entries
            await self._clean_access_cache()

        except Exception as e:
            self.log_warning(f"Failed to cache access result: {e}")

    async def _clean_access_cache(self):
        """Clean expired cache entries"""
        try:
            now = datetime.now()
            expired_keys = []

            for cache_key, cache_data in self.access_cache.items():
                cache_time = cache_data["timestamp"]
                ttl = cache_data["ttl"]

                if (now - cache_time).total_seconds() > ttl:
                    expired_keys.append(cache_key)

            for key in expired_keys:
                del self.access_cache[key]

        except Exception as e:
            self.log_warning(f"Cache cleanup failed: {e}")

    async def _record_usage(
        self,
        user_id: str,
        quota_type: QuotaType,
        amount: int,
        metadata: Dict[str, Any] = None
    ):
        """Record usage in history"""
        try:
            usage_record = {
                "timestamp": datetime.now().isoformat(),
                "user_id": user_id,
                "quota_type": quota_type.value,
                "amount": amount,
                "metadata": metadata or {}
            }

            self.usage_history.append(usage_record)

            # Limit history size
            if len(self.usage_history) > 10000:
                self.usage_history = self.usage_history[-5000:]

        except Exception as e:
            self.log_warning(f"Failed to record usage: {e}")

    # Analytics helper methods
    async def _calculate_tier_distribution(self) -> Dict[str, int]:
        """Calculate distribution of users across tiers"""
        try:
            distribution = {}

            for user_quota in self.user_quotas.values():
                tier = user_quota.tier.value
                distribution[tier] = distribution.get(tier, 0) + 1

            return distribution

        except Exception as e:
            self.log_error(f"Tier distribution calculation failed: {e}")
            return {}

    async def _calculate_quota_utilization(self) -> Dict[str, float]:
        """Calculate average quota utilization across users"""
        try:
            utilization = {}
            quota_totals = {}
            quota_counts = {}

            for user_quota in self.user_quotas.values():
                for quota_type, usage in user_quota.usage.items():
                    limit = user_quota.limits.get(quota_type, 1)
                    if limit > 0:
                        utilization_pct = (usage / limit) * 100

                        quota_key = quota_type.value
                        quota_totals[quota_key] = quota_totals.get(quota_key, 0) + utilization_pct
                        quota_counts[quota_key] = quota_counts.get(quota_key, 0) + 1

            # Calculate averages
            for quota_key in quota_totals.keys():
                if quota_counts[quota_key] > 0:
                    utilization[quota_key] = quota_totals[quota_key] / quota_counts[quota_key]

            return utilization

        except Exception as e:
            self.log_error(f"Quota utilization calculation failed: {e}")
            return {}

    async def _calculate_feature_usage(self) -> Dict[str, int]:
        """Calculate feature usage statistics"""
        try:
            feature_usage = {}

            # Count feature access from usage history
            for record in self.usage_history[-1000:]:  # Last 1000 records
                metadata = record.get("metadata", {})
                feature = metadata.get("feature")

                if feature:
                    feature_usage[feature] = feature_usage.get(feature, 0) + 1

            return feature_usage

        except Exception as e:
            self.log_error(f"Feature usage calculation failed: {e}")
            return {}

    async def _identify_upgrade_opportunities(self) -> List[Dict[str, Any]]:
        """Identify users who might benefit from tier upgrades"""
        try:
            opportunities = []

            for user_quota in self.user_quotas.values():
                # Check for high quota utilization
                high_usage_quotas = []

                for quota_type, usage in user_quota.usage.items():
                    limit = user_quota.limits.get(quota_type, 1)
                    if limit > 0 and (usage / limit) > 0.8:  # 80% utilization
                        high_usage_quotas.append(quota_type.value)

                # Check for overages
                has_overages = len(user_quota.overages) > 0

                if high_usage_quotas or has_overages:
                    opportunities.append({
                        "user_id": user_quota.user_id,
                        "current_tier": user_quota.tier.value,
                        "high_usage_quotas": high_usage_quotas,
                        "has_overages": has_overages,
                        "recommended_tier": await self._recommend_tier_upgrade(user_quota)
                    })

            return opportunities

        except Exception as e:
            self.log_error(f"Upgrade opportunity identification failed: {e}")
            return []

    async def _recommend_tier_upgrade(self, user_quota: UserQuota) -> str:
        """Recommend appropriate tier upgrade for user"""
        try:
            current_tier = user_quota.tier

            # Simple upgrade logic - move to next tier
            if current_tier == SubscriptionTier.FREE:
                return SubscriptionTier.STARTER.value
            elif current_tier == SubscriptionTier.STARTER:
                return SubscriptionTier.PRO.value
            elif current_tier == SubscriptionTier.PRO:
                return SubscriptionTier.ENTERPRISE.value
            else:
                return current_tier.value  # Already at highest tier

        except Exception:
            return "pro"  # Default recommendation

    async def cleanup(self):
        """Cleanup access control manager resources"""
        try:
            # Clear caches and temporary data
            self.access_cache.clear()
            self.usage_history.clear()

            self.log_info("Access control manager cleanup completed")

        except Exception as e:
            self.log_error(f"Access control manager cleanup failed: {e}")


# Global access control manager instance
access_control_manager = AccessControlManager()
