import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const BROWSER_AUTOMATION_SERVICE_URL = process.env.BROWSER_AUTOMATION_SERVICE_URL || 'http://localhost:8001';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const authCookie = cookieStore.get('sb-access-token');
    
    if (!authCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from auth cookie
    const { data: { user }, error: authError } = await supabase.auth.getUser(authCookie.value);
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { task, config_id, stream = false } = body;

    if (!task || !config_id) {
      return NextResponse.json({ error: 'Task and config_id are required' }, { status: 400 });
    }

    // Verify the config belongs to the user and has browser automation enabled
    const { data: config, error: configError } = await supabase
      .from('custom_api_configs')
      .select('id, user_id, browser_automation_enabled, name')
      .eq('id', config_id)
      .eq('user_id', user.id)
      .single();

    if (configError || !config) {
      return NextResponse.json({ error: 'Configuration not found' }, { status: 404 });
    }

    if (!config.browser_automation_enabled) {
      return NextResponse.json({ error: 'Browser automation is not enabled for this configuration' }, { status: 403 });
    }

    // Get user tier and validate access
    const { data: subscription, error: subError } = await supabase
      .from('user_subscriptions')
      .select('tier')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    const userTier = subscription?.tier || 'free';

    if (userTier === 'free') {
      return NextResponse.json({ error: 'Browser automation requires Starter plan or higher' }, { status: 403 });
    }

    // Get user's API keys and roles for this config
    const { data: apiKeys, error: keysError } = await supabase
      .from('api_keys')
      .select(`
        id,
        provider,
        predefined_model_id,
        temperature,
        label,
        api_key_encrypted,
        key_role_assignments (
          role_name
        )
      `)
      .eq('custom_api_config_id', config_id)
      .eq('status', 'active');

    if (keysError || !apiKeys || apiKeys.length === 0) {
      return NextResponse.json({ error: 'No active API keys found for this configuration' }, { status: 400 });
    }

    // Prepare the request for the browser automation service
    const browserAutomationRequest = {
      task,
      user_id: user.id,
      config_id,
      config_name: config.name,
      user_tier: userTier,
      api_keys: apiKeys.map(key => ({
        id: key.id,
        provider: key.provider,
        model: key.predefined_model_id,
        temperature: key.temperature,
        label: key.label,
        roles: key.key_role_assignments?.map((assignment: any) => assignment.role_name) || []
      })),
      stream
    };

    // Call the browser automation service
    const endpoint = stream ? '/api/v1/browser/execute/stream' : '/api/v1/browser/execute';
    const response = await fetch(`${BROWSER_AUTOMATION_SERVICE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(browserAutomationRequest)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Browser automation service error' }));
      return NextResponse.json(
        { error: errorData.error || 'Browser automation service failed' },
        { status: response.status }
      );
    }

    // If streaming, return the stream
    if (stream) {
      return new NextResponse(response.body, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    }

    // For non-streaming, return the JSON response
    const result = await response.json();
    return NextResponse.json(result);

  } catch (error) {
    console.error('Error executing browser automation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
