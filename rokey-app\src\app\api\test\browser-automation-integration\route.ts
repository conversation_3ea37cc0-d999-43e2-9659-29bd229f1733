import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const authCookie = cookieStore.get('sb-access-token');
    
    if (!authCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from auth cookie
    const { data: { user }, error: authError } = await supabase.auth.getUser(authCookie.value);
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { config_id } = await request.json();

    if (!config_id) {
      return NextResponse.json({ error: 'config_id is required' }, { status: 400 });
    }

    // Verify the config belongs to the user and has browser automation enabled
    const { data: config, error: configError } = await supabase
      .from('custom_api_configs')
      .select('id, user_id, browser_automation_enabled, name')
      .eq('id', config_id)
      .eq('user_id', user.id)
      .single();

    if (configError || !config) {
      return NextResponse.json({ error: 'Configuration not found' }, { status: 404 });
    }

    // Get user tier
    const { data: subscription, error: subError } = await supabase
      .from('user_subscriptions')
      .select('tier')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    const userTier = subscription?.tier || 'free';

    // Test browser automation classification
    const testMessages = [
      {
        role: 'user',
        content: 'Browse Amazon and find the price of iPhone 15'
      }
    ];

    // Call the chat completions API to test browser automation classification
    const chatResponse = await fetch(`${request.nextUrl.origin}/api/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': request.headers.get('Cookie') || '',
      },
      body: JSON.stringify({
        model: 'test-model',
        messages: testMessages,
        custom_api_config_id: config_id,
        stream: false
      })
    });

    const chatResult = await chatResponse.text();

    return NextResponse.json({
      success: true,
      config: {
        id: config.id,
        name: config.name,
        browser_automation_enabled: config.browser_automation_enabled
      },
      user_tier: userTier,
      test_classification: {
        status: chatResponse.status,
        response_preview: chatResult.substring(0, 500)
      },
      integration_status: 'complete',
      message: 'Browser automation integration test completed successfully'
    });

  } catch (error: any) {
    console.error('Browser automation integration test error:', error);
    return NextResponse.json(
      { error: 'Integration test failed', details: error.message },
      { status: 500 }
    );
  }
}
